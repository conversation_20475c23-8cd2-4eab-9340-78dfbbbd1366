{"rustc": 13226066032359371072, "features": "[\"default\", \"display\", \"std\"]", "declared_features": "[\"add\", \"add_assign\", \"as_ref\", \"constructor\", \"debug\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"full\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"mul\", \"mul_assign\", \"not\", \"std\", \"sum\", \"testing-helpers\", \"try_from\", \"try_into\", \"try_unwrap\", \"unwrap\"]", "target": 7165309211519594838, "profile": 1613925905003419231, "path": 15207691060954909386, "deps": [[14526174249165944584, "derive_more_impl", false, 5670626014434694822]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/derive_more-a51b1d461317898f/dep-lib-derive_more", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}