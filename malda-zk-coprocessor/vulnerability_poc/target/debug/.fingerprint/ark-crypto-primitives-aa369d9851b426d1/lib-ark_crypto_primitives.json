{"rustc": 13226066032359371072, "features": "[\"merlin\", \"snark\", \"sponge\", \"std\"]", "declared_features": "[\"ark-r1cs-std\", \"commitment\", \"crh\", \"default\", \"encryption\", \"hashbrown\", \"merkle_tree\", \"merlin\", \"parallel\", \"prf\", \"print-trace\", \"r1cs\", \"rayon\", \"signature\", \"snark\", \"sponge\", \"std\", \"tracing\"]", "target": 12232848036435804882, "profile": 15657897354478470176, "path": 5912250112640367307, "deps": [[647417929892486539, "ark_serialize", false, 5746774580150701713], [966925859616469517, "ahash", false, 4942022635274118840], [5502062331616315784, "ark_ff", false, 1443927009158133421], [8700459469608572718, "blake2", false, 4071648741357824072], [9234201994497484447, "merlin", false, 5633774490235995985], [9857275760291862238, "sha2", false, 14857828535702985917], [9889883805127379877, "ark_relations", false, 17155883423942628959], [13859769749131231458, "derivative", false, 12881640534711686890], [14614447123944235085, "ark_crypto_primitives_macros", false, 8417205533049619119], [15175849579008230926, "ark_std", false, 14782902624632631888], [16201565495264925093, "ark_snark", false, 5596141986696920557], [17475753849556516473, "digest", false, 16399973148787674308], [17532637862849517517, "ark_ec", false, 899489982041536076]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/ark-crypto-primitives-aa369d9851b426d1/dep-lib-ark_crypto_primitives", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}