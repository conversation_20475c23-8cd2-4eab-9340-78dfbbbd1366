{"rustc": 13226066032359371072, "features": "[\"default\", \"std\"]", "declared_features": "[\"circuit_debug\", \"cuda\", \"default\", \"metal\", \"metal_prefix_products\", \"prove\", \"std\", \"unstable\"]", "target": 11561838669794982184, "profile": 15657897354478470176, "path": 13956872161073377063, "deps": [[530211389790465181, "hex", false, 3379478178411117322], [2358608249731162897, "build_script_build", false, 2615131108731958327], [2828590642173593838, "cfg_if", false, 1903128207448990402], [6203123018298125816, "borsh", false, 8519115944570708915], [6511429716036861196, "bytemuck", false, 12960138789610421640], [7264961447180378050, "risc0_core", false, 8378881043914900907], [8606274917505247608, "tracing", false, 12003909106442140115], [8632578124021956924, "hex_literal", false, 3006963861266844182], [8700459469608572718, "blake2", false, 4071648741357824072], [9689903380558560274, "serde", false, 17336760598612483569], [9857275760291862238, "sha2", false, 14857828535702985917], [11109039379400878162, "risc0_zkvm_platform", false, 9197768903368608278], [11115194146618580017, "stability", false, 9544087991522615788], [13625485746686963219, "anyhow", false, 5527867187803302802], [17475753849556516473, "digest", false, 16399973148787674308], [17605717126308396068, "paste", false, 8483729510288934481], [18130209639506977569, "rand_core", false, 17861008474086901988]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/risc0-zkp-431c154f4a130792/dep-lib-risc0_zkp", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}