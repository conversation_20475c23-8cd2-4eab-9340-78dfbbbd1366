{"rustc": 13226066032359371072, "features": "[]", "declared_features": "[\"arbitrary\", \"default\", \"k256\", \"serde\", \"std\"]", "target": 17066601904469602418, "profile": 7136672544129150780, "path": 17701411052965628905, "deps": [[3524131406528303713, "alloy_eips", false, 7330453542902394486], [5623574172753995887, "alloy_network_primitives", false, 17067960673184960901], [5988724083763663492, "alloy_primitives", false, 10434214542319862556], [8569119365930580996, "serde_json", false, 18360485058444614106], [9689903380558560274, "serde", false, 17336760598612483569], [10806645703491011684, "thiserror", false, 6237539356624216404], [11293676373856528358, "derive_more", false, 5582655070990950649], [11709604483720470746, "op_alloy_consensus", false, 14663177459167079619], [11855076707701928598, "alloy_rpc_types_eth", false, 12976066759708650388], [16980811431682948333, "alloy_consensus", false, 17809315020120491392], [17732661510563293299, "alloy_serde", false, 8678197861036870878]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/op-alloy-rpc-types-f419f3bfe169e944/dep-lib-op_alloy_rpc_types", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}