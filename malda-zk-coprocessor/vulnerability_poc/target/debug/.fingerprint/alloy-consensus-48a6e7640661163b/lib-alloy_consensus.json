{"rustc": 13226066032359371072, "features": "[\"default\", \"k256\", \"serde\", \"std\"]", "declared_features": "[\"arbitrary\", \"crypto-backend\", \"default\", \"k256\", \"kzg\", \"secp256k1\", \"serde\", \"serde-bincode-compat\", \"serde_with\", \"std\"]", "target": 2929293942009581624, "profile": 103656143193338361, "path": 8840458113792277448, "deps": [[3434989764622224963, "k256", false, 2287460836323973076], [3524131406528303713, "alloy_eips", false, 7330453542902394486], [3722963349756955755, "once_cell", false, 12396378922497620399], [5597578105680351444, "alloy_rlp", false, 11546697201965881595], [5988724083763663492, "alloy_primitives", false, 10434214542319862556], [9689903380558560274, "serde", false, 17336760598612483569], [10806645703491011684, "thiserror", false, 6237539356624216404], [11293676373856528358, "derive_more", false, 5582655070990950649], [11669886560282861163, "alloy_tx_macros", false, 4855272634214394456], [12170264697963848012, "either", false, 6934450391360989281], [16055447317977473347, "alloy_trie", false, 3386300495450636528], [17732661510563293299, "alloy_serde", false, 8678197861036870878], [18125022703902813197, "auto_impl", false, 12467752895867406000]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/alloy-consensus-48a6e7640661163b/dep-lib-alloy_consensus", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}