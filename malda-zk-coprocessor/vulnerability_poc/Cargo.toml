[package]
name = "vulnerability_poc"
version = "0.1.0"
edition = "2021"

[workspace]

[[bin]]
name = "batch_multicall_dos_poc"
path = "../batch_multicall_dos_poc.rs"

[dependencies]
# Use the exact same versions as malda_utils to avoid conflicts
alloy-primitives = { version = "0.7", features = ["serde"] }
alloy-sol-types = "0.7"
k256 = { version = "0.13.3", features = ["ecdsa", "std"] }
malda_utils = { path = "../malda_utils" }
hex = "0.4"
tokio = { version = "1.0", features = ["full"] }
