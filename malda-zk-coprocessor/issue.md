This is not a design choice but a critical vulnerability that enables denial-of-service attacks against the Malda protocol.

Issue Verification
The vulnerability exists in the batch_call_get_proof_data() function where it decodes return data without proper length validation. validators.rs:657-659

The function performs multicalls to various contracts expecting them to return proof data as (U256, U256) tuples representing (amountIn, amountOut). validators.rs:642-645

The legitimate getProofData implementations in both mTokenGateway and mErc20Host contracts correctly return two uint256 values: mTokenGateway.sol:125-127 and mErc20Host.sol:119-121

Attack Vector
The vulnerability allows a single malicious token contract to brick the entire multicall system by:

Deploying a malicious ERC-20 that implements getProofData(address,uint32) but returns only 1 byte instead of 64 bytes
When the multicall executes, the malicious contract's response cannot be decoded as (U256, U256)
The abi_decode() panics due to insufficient data, causing the entire proof circuit to abort
All legitimate users are blocked from withdrawals as the multicall batch fails completely
Proof of Concept Structure
To create a PoC demonstrating this vulnerability:

1. Create a Malicious Token Contract:

contract MaliciousToken {  
    function getProofData(address, uint32) external pure returns (bytes memory) {  
        return hex"ff"; // Return only 1 byte instead of 64 bytes  
    }  
}
2. Test Setup:

Deploy the malicious token alongside legitimate tokens
Include the malicious token address in the asset vector parameter
Call batch_call_get_proof_data() with mixed legitimate and malicious assets
3. Expected Result:

The function should panic at the abi_decode() call when processing the malicious token's return data
The entire batch fails, preventing processing of legitimate proof data queries
This demonstrates the DoS impact where one "grief-token" can block all multicalls
Impact Assessment
This is a High severity vulnerability because:

Single point of failure: One malicious contract can disable the entire system
Blocking withdrawals: Legitimate users cannot access their funds
No mitigation: The current implementation has no fallback mechanism
Easy to exploit: Requires only deploying a simple malicious contract
Notes
The issue stems from using .expect() instead of proper error handling in the ABI decoding process. The function should implement graceful error handling to skip malicious responses while processing legitimate ones, or validate return data length before attempting to decode it. This vulnerability affects the core proof validation system that enables cross-chain operations in the Malda protocol.